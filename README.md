# LaminisLM - AWS Lambda Language Model Inference Service

## Overview

This project is an AWS Lambda function that provides a serverless API for text generation using a Hugging Face language model deployed on Amazon SageMaker. The function acts as a bridge between HTTP requests and a SageMaker inference endpoint, enabling scalable and cost-effective language model inference.

## Architecture

```
HTTP Request → API Gateway → AWS Lambda → SageMaker Endpoint → Hugging Face Model
```

The Lambda function:
- Receives HTTP requests with query parameters
- Forwards text prompts to a SageMaker inference endpoint
- Returns generated text responses

## Features

- **Serverless**: Built on AWS Lambda for automatic scaling and pay-per-use pricing
- **Language Model Integration**: Connects to Hugging Face PyTorch TGI (Text Generation Inference) model
- **Configurable Generation**: Supports various text generation parameters
- **JSON API**: Simple REST API interface

## API Specification

### Endpoint
The Lambda function expects to be invoked via API Gateway with query string parameters.

### Request Format
```
GET /?query=<your_text_prompt>
```

### Response Format
```json
{
  "statusCode": 200,
  "body": "<generated_text>"
}
```

## Configuration

### Model Parameters
The function uses the following default generation parameters:

```python
"parameters": {
    "max_new_tokens": 256,
    "top_p": 0.9,
    "temperature": 0.6,
    "top_k": 50,
    "repetion_penalty" : 1.03,
    "do_sample" : True
}
```

### SageMaker Endpoint
- **Endpoint Name**: `huggingface-pytorch-tgi-inference-2025-06-24-08-30-53-965`
- **Region**: `us-east-1`
- **Model Type**: Hugging Face PyTorch TGI

## Dependencies

- `boto3` - AWS SDK for Python
- `json` - JSON handling (built-in)

## Deployment

This Lambda function is designed to be deployed in the AWS ecosystem:

1. **Lambda Function**: The main inference logic
2. **SageMaker Endpoint**: Hosts the Hugging Face language model
3. **API Gateway** (recommended): Provides HTTP interface
4. **IAM Roles**: Required permissions for SageMaker access

## Required AWS Permissions

The Lambda execution role needs:
- `sagemaker:InvokeEndpoint` permission for the specified endpoint
- Basic Lambda execution permissions

## Usage Example

```bash
curl "https://your-api-gateway-url/?query=Tell me a story about"
```

Response:
```json
{
  "statusCode": 200,
  "body": "Tell me a story about a brave knight who discovered..."
}
```

## Notes

- The function expects query parameters in the event structure typical of API Gateway integration
- Generated text is returned as the first element of the predictions array
- Error handling could be enhanced for production use
- The SageMaker endpoint name is hardcoded and may need updates if the endpoint is recreated

## Project Structure

```
laminislm/
└── lambda_function.py    # Main Lambda handler function
```

This is a minimal, focused implementation designed for serverless text generation inference using AWS managed services.
